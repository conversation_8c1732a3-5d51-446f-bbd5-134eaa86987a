<template>
    <div>
        <div class="flex items-center">
            <span>无人机搜索</span>
            <div class="ml-4">
                <el-input v-model="formData.title" placeholder="请输入无人机名称">
                    <template #append>
                        <el-button :icon="Search" @click="resetPage" />
                    </template>
                </el-input>
            </div>
        </div>
        <div class="mt-4">
            <el-scrollbar height="380px" class="mt-[20px]">
                <el-table :data="pager.lists" style="width: 100%" @row-click="rowClick">
                    <el-table-column min-width="40">
                        <template #default="{ row }">
                            <el-radio :label="row.id" :model-value="tableSelect">&nbsp;</el-radio>
                        </template>
                    </el-table-column>
                    <el-table-column label="无人机图片" min-width="80">
                        <template #default="{ row }">
                            <div>
                                <el-image
                                    v-if="row.images && row.images.length > 0"
                                    :src="row.images[0]"
                                    class="w-[40px] h-[40px]"
                                    fit="cover"
                                />
                                <div v-else class="w-[40px] h-[40px] bg-gray-100 flex items-center justify-center text-gray-400 text-xs">
                                    暂无图片
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="无人机名称" min-width="150">
                        <template #default="{ row }">
                            <span>{{ row.title }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="押金" min-width="100">
                        <template #default="{ row }">
                            <span>¥{{ row.deposit }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="租用单价" min-width="100">
                        <template #default="{ row }">
                            <span>¥{{ row.price }}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </el-scrollbar>
        </div>
        <div class="flex justify-end">
            <pagination
                v-model="pager"
                @change="getLists"
                layout="total, prev, pager, next, jumper"
            />
        </div>
    </div>
</template>

<script lang="ts" setup name="DroneList">
import { Search } from '@element-plus/icons-vue'
import { apiDroneLists } from '@/api/marketing/drone'
import { usePaging } from '@/hooks/usePaging'
import type { PropType } from 'vue'
import { LinkTypeEnum, type Link } from '.'

const props = defineProps({
    modelValue: {
        type: Object as PropType<Link>,
        default: () => ({})
    }
})
const emit = defineEmits<{
    (event: 'update:modelValue', value: Link): void
}>()

const formData = ref({
    title: '' // 无人机名称
})

const tableSelect = ref()
const rowClick = (row: any) => {
    tableSelect.value = row.id
    console.log('row', row)

    emit('update:modelValue', {
        path: `/subpack/pages/lease/goods_detail?id=${row.id}&is_disable=1`,
        query: {
            id: row.id,
            title: row.title
        },
        type: LinkTypeEnum.DRONE_LIST
    })
}

// 监听modelValue变化，设置选中状态
watch(
    () => props.modelValue,
    (value) => {
        if (value?.query?.id) {
            tableSelect.value = value.query.id
        }
    },
    {
        immediate: true
    }
)

//分页组件
const { pager, getLists, resetPage, resetParams } = usePaging({
    fetchFun: apiDroneLists,
    params: formData
})

getLists()
</script>

<style></style>
