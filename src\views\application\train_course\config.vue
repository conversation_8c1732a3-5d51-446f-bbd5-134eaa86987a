<template>
    <div>
        <el-card class="!border-none" shadow="never">
            <el-alert
                type="warning"
                title="配置不同类型无人机的培训课程价格，包括课程名称、内容和费用等信息。"
                :closable="false"
                show-icon
            ></el-alert>
        </el-card>

        <el-card
            class="!border-none"
            shadow="never"
            body-style="padding: 0"
            style="margin-top: 15px"
        >
            <div class="ls-card m-t-24">
                <div class="nr weight-500 m-b-20">课程价格配置</div>
                <el-form ref="formRef" :model="form" label-width="120px">
                    <el-form-item label="无人机类型">
                        <div class="flex">
                            <div
                                v-for="(item, index) in droneTypeNames"
                                :key="index"
                                class="option-item"
                                @click="handleClick(index)"
                                :class="activeIndex == index ? 'actived' : ''"
                            >
                                <span style="line-height: 20px">{{ item }}</span>
                                <span style="line-height: 20px" class="muted xxs">
                                    ¥{{ form[index]?.price || '0.00' }}
                                </span>
                            </div>
                        </div>
                    </el-form-item>
                    
                    <el-form-item label="课程名称">
                        <el-input
                            class="w-[400px]"
                            maxlength="100"
                            show-word-limit
                            v-model="form[activeIndex].name"
                            placeholder="请输入课程名称"
                            v-input-filter="'text'"
                        />
                    </el-form-item>

                    <el-form-item label="课程费用">
                        <el-input-number
                            v-model="form[activeIndex].price"
                            :min="0"
                            :precision="2"
                            placeholder="请输入课程费用"
                            class="w-[400px]"
                        />
                        <span class="ml-2 text-gray-500">元</span>
                    </el-form-item>

                    <el-form-item label="课程内容">
                        <el-input
                            type="textarea"
                            :autosize="{ minRows: 4, maxRows: 8 }"
                            maxlength="1000"
                            show-word-limit
                            v-model="form[activeIndex].content"
                            placeholder="请输入课程内容描述"
                            class="w-[600px]"
                            v-input-filter="'text'"
                        />
                    </el-form-item>
                </el-form>
            </div>
        </el-card>

        <footer-btns>
            <el-button type="primary" @click="onSubmit">保存</el-button>
        </footer-btns>
    </div>
</template>

<script lang="ts" setup name="trainCourseConfig">
import { ref } from 'vue'
import { apiTrainCourseGetConfig, apiTrainCourseSetConfig } from '@/api/application'

const activeIndex = ref(0)
const droneTypeNames = ref(['小型无人机', '中型无人机', '大型无人机'])

const form = ref([
    {
        id: '',
        name: '',
        drone_type_id: 1,
        content: '',
        price: 0
    },
    {
        id: '',
        name: '',
        drone_type_id: 2,
        content: '',
        price: 0
    },
    {
        id: '',
        name: '',
        drone_type_id: 3,
        content: '',
        price: 0
    }
])

// 获取课程配置
const getTrainCourseConfig = async () => {
    try {
        const data = await apiTrainCourseGetConfig()
        if (data && Array.isArray(data)) {
            // 将数组数据赋值给form
            data.forEach((item, index) => {
                if (index < form.value.length) {
                    form.value[index] = { ...form.value[index], ...item }
                }
            })
        }
    } catch (error) {
        console.error('获取课程配置失败:', error)
    }
}

// 切换无人机类型
const handleClick = (val: number) => {
    activeIndex.value = val
}

// 保存配置
const onSubmit = async () => {
    try {
        await apiTrainCourseSetConfig({ courses: form.value })
        ElMessage.success('保存成功')
        getTrainCourseConfig()
    } catch (error) {
        console.error('保存失败:', error)
    }
}

// 初始化
onMounted(() => {
    getTrainCourseConfig()
})
</script>

<style lang="scss" scoped>
.option-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 10px;
    padding: 15px 30px;
    margin-right: 20px;
    border: 1px solid rgba(238, 238, 238, 1);
    cursor: pointer;
    min-width: 120px;
}

.actived {
    background-color: #f5f8ff;
    border: 1px solid rgba(64, 115, 250, 1);
}

.muted {
    color: #909399;
}

.xxs {
    font-size: 12px;
}
</style>
