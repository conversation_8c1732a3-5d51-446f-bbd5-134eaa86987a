/**
 * 输入过滤工具函数
 * 用于过滤JS、超链接，处理金额和数字输入
 */

/**
 * 过滤JS脚本和超链接
 * @param value 输入值
 * @returns 过滤后的值
 */
export function filterJsAndLinks(value: string): string {
    if (!value) return value
    
    let filtered = value
    
    // 过滤<script>标签及其内容
    filtered = filtered.replace(/<script\b[^>]*>[\s\S]*?<\/script>/gi, '')
    
    // 过滤javascript:协议（在任何地方）
    filtered = filtered.replace(/javascript\s*:/gi, '')
    
    // 过滤data:协议（可能用于执行JS）
    filtered = filtered.replace(/data\s*:\s*text\/html/gi, '')
    
    // 过滤vbscript:协议
    filtered = filtered.replace(/vbscript\s*:/gi, '')
    
    // 过滤所有事件属性（onclick、onload等）
    filtered = filtered.replace(/<([^>]+)\s+on\w+\s*=\s*["'][^"']*["']/gi, '<$1')
    
    // 过滤<a>标签的href属性，只保留文本内容
    filtered = filtered.replace(/<a\b[^>]*>([\s\S]*?)<\/a>/gi, '$1')
    
    // 过滤iframe标签
    filtered = filtered.replace(/<iframe\b[^>]*>[\s\S]*?<\/iframe>/gi, '')
    
    // 过滤object、embed、applet等标签
    filtered = filtered.replace(/<(object|embed|applet)\b[^>]*>[\s\S]*?<\/\1>/gi, '')
    
      
    return filtered
}

/**
 * 过滤金额输入，只允许数字和小数点，保留两位小数
 * @param value 输入值
 * @returns 格式化后的金额
 */
export function filterMoneyInput(value: string): string {
    if (!value) return value
    
    // 移除非数字和小数点的字符
    let filtered = value.replace(/[^\d.]/g, '')
    
    // 确保只有一个小数点
    const dotIndex = filtered.indexOf('.')
    if (dotIndex !== -1) {
        filtered = filtered.substring(0, dotIndex + 1) + filtered.substring(dotIndex + 1).replace(/\./g, '')
    }
    
    // 限制两位小数
    if (dotIndex !== -1 && filtered.length > dotIndex + 3) {
        filtered = filtered.substring(0, dotIndex + 3)
    }
    
    return filtered
}

/**
 * 过滤数字输入，只允许数字
 * @param value 输入值
 * @returns 过滤后的数字
 */
export function filterNumberInput(value: string): string {
    if (!value) return value
    
    // 移除非数字字符
    return value.replace(/\D/g, '')
}

/**
 * 格式化金额为标准格式（两位小数）
 * @param value 金额值
 * @returns 格式化后的金额
 */
export function formatMoney(value: string | number): string {
    if (!value) return '0.00'
    
    const num = parseFloat(value.toString())
    if (isNaN(num)) return '0.00'
    
    return num.toFixed(2)
}

/**
 * 通用输入过滤器
 * @param value 输入值
 * @param type 过滤类型：'text' | 'money' | 'number'
 * @returns 过滤后的值
 */
export function filterInput(value: string, type: 'text' | 'money' | 'number' = 'text'): string {
    if (!value) return value
    
    switch (type) {
        case 'money':
            return filterMoneyInput(value)
        case 'number':
            return filterNumberInput(value)
        case 'text':
        default:
            return filterJsAndLinks(value)
    }
}