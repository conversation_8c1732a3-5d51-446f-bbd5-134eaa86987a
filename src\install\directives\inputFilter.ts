/**
 * 输入过滤指令
 * 用于在输入时实时过滤内容
 */

import type { Directive, DirectiveBinding } from 'vue'
import { filterInput } from '@/utils/inputFilter'

export interface InputFilterBinding {
    value: 'text' | 'money' | 'number'
}

// 设置事件监听器的函数
function setupEventListeners(targetEl: HTMLInputElement | HTMLTextAreaElement, filterType: InputFilterBinding['value']) {
    const actualFilterType = filterType || 'text'
    
    // 创建一个处理输入的函数
    const handleInput = (event: Event) => {
        const target = event.target as HTMLInputElement
        const originalValue = target.value
        
        const filteredValue = filterInput(originalValue, actualFilterType)
        
        // 如果值被改变了，更新输入框的值
        if (originalValue !== filteredValue) {
            target.value = filteredValue
            // 触发input事件以确保v-model更新
            target.dispatchEvent(new Event('input', { bubbles: true }))
        }
    }
    
    // 创建一个处理粘贴的函数
    const handlePaste = (event: ClipboardEvent) => {
        // 先获取粘贴的文本
        const pastedText = event.clipboardData?.getData('text/plain') || ''
        
        // 过滤文本
        const filteredText = filterInput(pastedText, actualFilterType)
        
        // 如果过滤后的文本为空，阻止默认粘贴行为
        if (!filteredText) {
            event.preventDefault()
            return
        }
        
        // 如果文本被过滤改变了，阻止默认行为并手动插入
        if (pastedText !== filteredText) {
            event.preventDefault()
            
            // 获取当前光标位置和选中内容（添加兼容性检查）
            const start = typeof targetEl.selectionStart === 'number' ? targetEl.selectionStart : 0
            const end = typeof targetEl.selectionEnd === 'number' ? targetEl.selectionEnd : 0
            const currentValue = targetEl.value || ''
            
            // 替换选中的内容或在光标位置插入
            const newValue = currentValue.substring(0, start) + filteredText + currentValue.substring(end)
            
            // 更新输入框的值
            targetEl.value = newValue
            
            // 设置光标位置到插入内容之后（仅对输入框有效）
            const newCursorPos = start + filteredText.length
            if (typeof targetEl.setSelectionRange === 'function') {
                targetEl.setSelectionRange(newCursorPos, newCursorPos)
            }
            
            // 触发input事件以确保v-model更新
            targetEl.dispatchEvent(new Event('input', { bubbles: true }))
        }
    }
    
    // 保存处理函数到元素上，以便后续移除
    (targetEl as any)._inputFilterHandlers = {
        input: handleInput,
        paste: handlePaste
    }
    
    // 添加事件监听器到实际的input元素上
    targetEl.addEventListener('input', handleInput)
    targetEl.addEventListener('paste', handlePaste)
}

export const vInputFilter: Directive<HTMLInputElement | HTMLTextAreaElement, InputFilterBinding['value']> = {
    mounted(el: HTMLInputElement | HTMLTextAreaElement, binding: DirectiveBinding<InputFilterBinding['value']>) {
        // 如果是Element Plus的el-input或el-textarea组件，需要找到实际的input/textarea元素
        let targetEl: HTMLInputElement | HTMLTextAreaElement = el
        
        // 检查当前元素是否是输入框或文本域，如果不是，尝试查找子元素中的input或textarea
        if (el.tagName !== 'INPUT' && el.tagName !== 'TEXTAREA') {
            // 使用setTimeout确保DOM完全渲染
            setTimeout(() => {
                // 优先查找input，然后查找textarea
                const inputEl = el.querySelector('input') as HTMLInputElement
                const textareaEl = el.querySelector('textarea') as HTMLTextAreaElement
                
                if (inputEl) {
                    targetEl = inputEl
                    setupEventListeners(targetEl, binding.value)
                } else if (textareaEl) {
                    targetEl = textareaEl
                    setupEventListeners(targetEl, binding.value)
                } else {
                    console.warn('v-input-filter 指令未找到有效的input或textarea元素')
                }
            }, 50)
            return
        }
        
        // 检查元素是否支持输入框相关方法
        if (!('value' in targetEl) || typeof targetEl.value !== 'string') {
            console.warn('v-input-filter 指令只能应用于输入框或文本域元素')
            return
        }
        
        // 直接设置事件监听器
        setupEventListeners(targetEl, binding.value)
    },
    
    updated(el: HTMLInputElement | HTMLTextAreaElement, binding: DirectiveBinding<InputFilterBinding['value']>) {
        // 如果指令的值改变了，更新过滤器类型
        const filterType = binding.value || 'text'
        if ((el as any)._inputFilterType !== filterType) {
            (el as any)._inputFilterType = filterType
        }
    },
    
    unmounted(el: HTMLInputElement | HTMLTextAreaElement) {
        // 移除事件监听器
        // 首先检查当前元素是否有事件处理器
        const handlers = (el as any)._inputFilterHandlers
        if (handlers) {
            el.removeEventListener('input', handlers.input)
            el.removeEventListener('paste', handlers.paste)
            delete (el as any)._inputFilterHandlers
        }
        
        // 如果是el-input或el-textarea组件，还需要检查内部input或textarea元素
        if (el.tagName !== 'INPUT' && el.tagName !== 'TEXTAREA') {
            const inputEl = el.querySelector('input') as HTMLInputElement
            const textareaEl = el.querySelector('textarea') as HTMLTextAreaElement
            
            if (inputEl) {
                const inputHandlers = (inputEl as any)._inputFilterHandlers
                if (inputHandlers) {
                    inputEl.removeEventListener('input', inputHandlers.input)
                    inputEl.removeEventListener('paste', inputHandlers.paste)
                    delete (inputEl as any)._inputFilterHandlers
                }
            }
            
            if (textareaEl) {
                const textareaHandlers = (textareaEl as any)._inputFilterHandlers
                if (textareaHandlers) {
                    textareaEl.removeEventListener('input', textareaHandlers.input)
                    textareaEl.removeEventListener('paste', textareaHandlers.paste)
                    delete (textareaEl as any)._inputFilterHandlers
                }
            }
        }
    }
}

// 默认导出
export default vInputFilter