<template>
    <el-card shadow="never" class="!border-none">
        <el-page-header :content="pageTitle" @back="$router.back()" />
    </el-card>

    <el-card shadow="never" style="margin-top: 15px" class="!border-none">
        <div class="pl-[80px] pr-[100px]">
            <el-form
                ref="formRef"
                :model="formData"
                :rules="formRules"
                label-width="120px"
                class="mt-6"
            >
                <el-form-item label="合伙人名称" prop="name">
                    <el-input
                        v-model="formData.name"
                        placeholder="请输入合伙人名称"
                        maxlength="50"
                        show-word-limit
                        class="w-80"
                        v-input-filter="'text'"
                    />
                </el-form-item>

                <el-form-item label="手机号码" prop="mobile">
                    <el-input
                        v-model="formData.mobile"
                        placeholder="请输入手机号码"
                        maxlength="11"
                        class="w-80"
                        v-input-filter="'number'"
                    />
                </el-form-item>

                <el-form-item label="登录账号" prop="account">
                    <el-input
                        v-model="formData.account"
                        placeholder="请输入登录账号"
                        maxlength="50"
                        class="w-80"
                    />
                </el-form-item>

                <el-form-item label="登录密码" prop="password">
                    <el-input
                        v-model="formData.password"
                        type="password"
                        :placeholder="isEdit ? '留空则不修改密码' : '请输入登录密码'"
                        maxlength="32"
                        show-password
                        class="w-80"
                    />
                </el-form-item>

                <el-form-item label="合伙人状态" prop="status">
                    <el-radio-group v-model="formData.status">
                        <el-radio :label="0">启用</el-radio>
                        <el-radio :label="1">禁用</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item label="合伙人区域" prop="area_ids">
                    <div class="w-full">
                        <div class="area-selection-container">
                            <!-- 选择统计 -->
                            <div class="selection-actions mb-4">
                                <span class="text-sm text-gray-600">
                                    已选择 {{ formData.area_ids.length }} 个区域
                                </span>
                            </div>

                            <!-- 区域选择 -->
                            <el-checkbox-group v-model="formData.area_ids" class="area-checkbox-group">
                                <div v-for="province in areaOptions" :key="province.value" class="province-group">
                                    <!-- 省份标题和操作 -->
                                    <div class="province-header">
                                        <div class="province-title">
                                            <span class="province-name">{{ province.label }}</span>
                                            <div class="province-actions">
                                                <el-button
                                                    size="small"
                                                    text
                                                    type="primary"
                                                    @click="selectProvince(province)"
                                                >
                                                    全选省
                                                </el-button>
                                                <el-button
                                                    size="small"
                                                    text
                                                    type="danger"
                                                    @click="clearProvince(province)"
                                                >
                                                    清空省
                                                </el-button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 城市列表 -->
                                    <div class="cities-container">
                                        <div v-for="city in province.children" :key="city.value" class="city-group">
                                            <!-- 城市标题和操作 -->
                                            <div class="city-header">
                                                <div class="city-title">
                                                    <span class="city-name">{{ city.label }}</span>
                                                    <div class="city-actions">
                                                        <el-button
                                                            size="small"
                                                            text
                                                            type="primary"
                                                            @click="selectCity(city)"
                                                        >
                                                            全选市
                                                        </el-button>
                                                        <el-button
                                                            size="small"
                                                            text
                                                            type="danger"
                                                            @click="clearCity(city)"
                                                        >
                                                            清空市
                                                        </el-button>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 区县列表 -->
                                            <div class="districts-container">
                                                <el-checkbox
                                                    v-for="district in city.children"
                                                    :key="district.value"
                                                    :label="district.value"
                                                    :disabled="isAreaDisabled(district.value)"
                                                    class="district-checkbox"
                                                >
                                                    {{ district.label }}
                                                </el-checkbox>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-checkbox-group>

                            <div class="text-sm text-gray-500 mt-2">
                                注：灰色选项表示该区域已被其他合伙人使用，无法选择
                            </div>
                        </div>
                    </div>
                </el-form-item>
            </el-form>
        </div>
    </el-card>

    <footer-btns>
        <el-button @click="$router.back()">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ isEdit ? '保存' : '添加' }}
        </el-button>
    </footer-btns>
</template>

<script lang="ts" setup>
import { apiPartnerDetail, apiPartnerSave, apiPartnerGetArea } from '@/api/master_worker/partner'
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import FooterBtns from '@/components/footer-btns/index.vue'
import feedback from '@/utils/feedback'
import type { ElForm } from 'element-plus'
import areaData from '@/utils/area'

const route = useRoute()
const router = useRouter()
const formRef = ref<InstanceType<typeof ElForm>>()

const id = route.query.id as string
const isEdit = computed(() => !!id)
const pageTitle = computed(() => isEdit.value ? '编辑合伙人' : '添加合伙人')

const submitLoading = ref(false)
const areaOptions = ref<any[]>([])
const usedAreaIds = ref<number[]>([])

const formData = ref({
    id: id || '',
    name: '',
    mobile: '',
    account: '',
    password: '',
    status: 0,
    area_ids: [] as number[]
})

const formRules = {
    name: [
        { required: true, message: '请输入合伙人名称', trigger: 'blur' }
    ],
    mobile: [
        { required: true, message: '请输入手机号码', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    account: [
        { required: true, message: '请输入登录账号', trigger: 'blur' }
    ],
    password: [
        {
            required: !isEdit.value,
            message: '请输入登录密码',
            trigger: 'blur'
        },
        {
            validator: (_rule: any, value: string, callback: any) => {
                // 编辑模式下，如果密码为空则跳过验证
                if (isEdit.value && !value) {
                    callback()
                    return
                }
                // 如果有值，则验证长度
                if (value && value.length < 6) {
                    callback(new Error('密码长度不能少于6位'))
                    return
                }
                callback()
            },
            trigger: 'blur'
        }
    ],
    status: [
        { required: true, message: '请选择合伙人状态', trigger: 'change' }
    ],
    area_ids: [
        { required: true, message: '请选择合伙人区域', trigger: 'change' }
    ]
}

// 判断区域是否被禁用
const isAreaDisabled = (areaId: number) => {
    // 如果是编辑模式，当前合伙人已选择的区域不禁用
    if (isEdit.value && formData.value.area_ids.includes(areaId)) {
        return false
    }
    // 其他已使用的区域禁用
    return usedAreaIds.value.includes(areaId)
}



// 选择整个省份
const selectProvince = (province: any) => {
    const provinceIds: number[] = []
    province.children?.forEach((city: any) => {
        city.children?.forEach((district: any) => {
            if (!isAreaDisabled(district.value)) {
                provinceIds.push(district.value)
            }
        })
    })
    formData.value.area_ids = [...new Set([...formData.value.area_ids, ...provinceIds])]
}

// 清空整个省份
const clearProvince = (province: any) => {
    const provinceIds: number[] = []
    province.children?.forEach((city: any) => {
        city.children?.forEach((district: any) => {
            provinceIds.push(district.value)
        })
    })
    formData.value.area_ids = formData.value.area_ids.filter(id => !provinceIds.includes(id))
}

// 选择整个城市
const selectCity = (city: any) => {
    const cityIds: number[] = []
    city.children?.forEach((district: any) => {
        if (!isAreaDisabled(district.value)) {
            cityIds.push(district.value)
        }
    })
    formData.value.area_ids = [...new Set([...formData.value.area_ids, ...cityIds])]
}

// 清空整个城市
const clearCity = (city: any) => {
    const cityIds: number[] = []
    city.children?.forEach((district: any) => {
        cityIds.push(district.value)
    })
    formData.value.area_ids = formData.value.area_ids.filter(id => !cityIds.includes(id))
}

// 获取区域数据
const getAreaOptions = () => {
    areaOptions.value = areaData
}

// 获取已使用的区域
const getUsedAreas = async () => {
    try {
        const result = await apiPartnerGetArea()
        usedAreaIds.value = result || []
    } catch (error) {
        console.error('获取已使用区域失败:', error)
    }
}

// 获取详情
const getDetail = async () => {
    if (!id) return

    try {
        const result = await apiPartnerDetail({ id })
        // 排除密码字段，不回填密码
        const { password, ...detailData } = result
        Object.assign(formData.value, detailData)
    } catch (error) {
        feedback.msgError('获取详情失败')
        router.back()
    }
}

// 提交表单
const handleSubmit = async () => {
    try {
        await formRef.value?.validate()
        submitLoading.value = true

        await apiPartnerSave(formData.value)
        router.back()
    } catch (error) {
        // 表单验证失败或提交失败
    } finally {
        submitLoading.value = false
    }
}

onMounted(async () => {
    getAreaOptions()
    await getUsedAreas()
    if (isEdit.value) {
        await getDetail()
    }
})
</script>

<style lang="scss" scoped>
.area-selection-container {
    .selection-actions {
        padding: 12px;
        background-color: #f5f7fa;
        border-radius: 4px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .area-checkbox-group {
        max-height: 500px;
        overflow-y: auto;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        padding: 16px;

        .province-group {
            margin-bottom: 24px;
            border-bottom: 1px solid #ebeef5;
            padding-bottom: 16px;

            &:last-child {
                border-bottom: none;
                margin-bottom: 0;
            }

            .province-header {
                margin-bottom: 16px;

                .province-title {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px 12px;
                    background-color: #f0f2f5;
                    border-radius: 4px;

                    .province-name {
                        font-weight: bold;
                        font-size: 16px;
                        color: #303133;
                    }

                    .province-actions {
                        display: flex;
                        gap: 8px;
                    }
                }
            }

            .cities-container {
                padding-left: 16px;

                .city-group {
                    margin-bottom: 16px;

                    .city-header {
                        margin-bottom: 8px;

                        .city-title {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            padding: 6px 10px;
                            background-color: #fafafa;
                            border-radius: 4px;

                            .city-name {
                                font-weight: 500;
                                font-size: 14px;
                                color: #606266;
                            }

                            .city-actions {
                                display: flex;
                                gap: 6px;
                            }
                        }
                    }

                    .districts-container {
                        padding-left: 16px;
                        display: flex;
                        flex-wrap: wrap;
                        gap: 8px;

                        .district-checkbox {
                            margin-right: 0;
                            margin-bottom: 0;
                            min-width: 120px;

                            :deep(.el-checkbox__label) {
                                font-size: 13px;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
