<template>
    <el-card class="!border-none" shadow="never">
        <el-page-header content="飞手详情" @back="$router.back()" />
    </el-card>

    <el-card shadow="never" class="mt-4 !border-none">
        <template #header>
            <span class="text-lg font-semibold">基本资料</span>
        </template>
        <el-form label-position="right" label-width="auto">
            <el-form-item label="飞手ID:">
                {{ detail.id }}
            </el-form-item>
            <el-form-item label="头像:">
                <div class="w-[80px] h-[80px]" v-if="detail.work_image">
                    <el-image
                        style="width: 80px; height: 80px"
                        :src="detail.work_image"
                        :preview-src-list="[detail.work_image]"
                        :hide-on-click-modal="true"
                        :preview-teleported="true"
                        :fit="'cover'"
                    />
                </div>
                <span v-else>-</span>
            </el-form-item>
            <el-form-item label="昵称:">
                {{ detail.name || '-' }}
            </el-form-item>
            <el-form-item label="手机号码:">
                {{ detail.mobile || '-' }}
            </el-form-item>
            <el-form-item label="无人机数量:">
                {{ detail.drone_num || 0 }}
            </el-form-item>
            <el-form-item label="性别:">
                {{ detail.sex_desc || '-' }}
            </el-form-item>
            <el-form-item label="飞手类型:">
                {{ detail.apply_type == 1 ? "个人" : "公司" }}
            </el-form-item>
            <el-form-item label="无人机型号:">
                {{ getDroneTypeName(detail.drone_type_id) }}
            </el-form-item>
        </el-form>
    </el-card>

    <el-card shadow="never" class="mt-4 !border-none">
        <template #header>
            <span class="text-lg font-semibold">身份证照片</span>
        </template>
        <el-form label-position="right" label-width="auto">
            <el-form-item label="身份证正面:">
                <div class="w-[200px] h-[120px]" v-if="detail.identity_portrait_image">
                    <el-image
                        style="width: 200px; height: 120px"
                        :src="detail.identity_portrait_image"
                        :preview-src-list="[detail.identity_portrait_image]"
                        :hide-on-click-modal="true"
                        :preview-teleported="true"
                        :fit="'cover'"
                    />
                </div>
                <span v-else>-</span>
            </el-form-item>
            <el-form-item label="身份证反面:">
                <div class="w-[200px] h-[120px]" v-if="detail.identity_emblem_image">
                    <el-image
                        style="width: 200px; height: 120px"
                        :src="detail.identity_emblem_image"
                        :preview-src-list="[detail.identity_emblem_image]"
                        :hide-on-click-modal="true"
                        :preview-teleported="true"
                        :fit="'cover'"
                    />
                </div>
                <span v-else>-</span>
            </el-form-item>
        </el-form>
    </el-card>

    <el-card shadow="never" class="mt-4 !border-none">
        <template #header>
            <span class="text-lg font-semibold">签署合同</span>
        </template>
        <el-form label-position="right" label-width="auto">
            <el-form-item label="合同图片:">
                <div v-if="detail.contract_image && detail.contract_image.length > 0" class="flex flex-wrap gap-2">
                    <div
                        v-for="(image, index) in detail.contract_image"
                        :key="index"
                        class="w-[200px] h-[120px]"
                    >
                        <el-image
                            style="width: 200px; height: 120px"
                            :src="image"
                            :preview-src-list="detail.contract_image"
                            :hide-on-click-modal="true"
                            :preview-teleported="true"
                            :fit="'cover'"
                        />
                    </div>
                </div>
                <span v-else>-</span>
            </el-form-item>
        </el-form>
    </el-card>

    <!-- 营业执照 - 仅在飞手类型为公司时显示 -->
    <el-card shadow="never" class="mt-4 !border-none" v-if="detail.apply_type == 2 && detail.business_img">
        <template #header>
            <span class="text-lg font-semibold">营业执照</span>
        </template>
        <el-form label-position="right" label-width="auto">
            <el-form-item label="营业执照:">
                <div class="w-[200px] h-[120px]">
                    <el-image
                        style="width: 200px; height: 120px"
                        :src="detail.business_img"
                        :preview-src-list="[detail.business_img]"
                        :hide-on-click-modal="true"
                        :preview-teleported="true"
                        :fit="'cover'"
                    />
                </div>
            </el-form-item>
        </el-form>
    </el-card>

    <!-- 驾驶证 -->
    <el-card shadow="never" class="mt-4 !border-none" v-if="detail.drive_img">
        <template #header>
            <span class="text-lg font-semibold">驾驶证</span>
        </template>
        <el-form label-position="right" label-width="auto">
            <el-form-item label="驾驶证:">
                <div class="w-[200px] h-[120px]">
                    <el-image
                        style="width: 200px; height: 120px"
                        :src="detail.drive_img"
                        :preview-src-list="[detail.drive_img]"
                        :hide-on-click-modal="true"
                        :preview-teleported="true"
                        :fit="'cover'"
                    />
                </div>
            </el-form-item>
        </el-form>
    </el-card>

    <!-- 无人机照片 -->
    <el-card shadow="never" class="mt-4 !border-none" v-if="detail.drone_img && detail.drone_img.length > 0">
        <template #header>
            <span class="text-lg font-semibold">无人机照片</span>
        </template>
        <el-form label-position="right" label-width="auto">
            <el-form-item label="无人机照片:">
                <div class="flex flex-wrap gap-2">
                    <div
                        v-for="(image, index) in detail.drone_img"
                        :key="index"
                        class="w-[200px] h-[120px]"
                    >
                        <el-image
                            style="width: 200px; height: 120px"
                            :src="image"
                            :preview-src-list="detail.drone_img"
                            :hide-on-click-modal="true"
                            :preview-teleported="true"
                            :fit="'cover'"
                        />
                    </div>
                </div>
            </el-form-item>
        </el-form>
    </el-card>

    <!-- 无人机列表 -->
    <el-card shadow="never" class="mt-4 !border-none">
        <template #header>
            <span class="text-lg font-semibold">无人机列表</span>
        </template>
        <el-table :data="droneList" v-loading="droneLoading" style="width: 100%">
            <el-table-column prop="name" label="无人机名称" min-width="120" />
            <el-table-column prop="brand" label="品牌" min-width="100" />
            <el-table-column prop="code" label="编码" min-width="120" />
            <el-table-column label="无人机图片" min-width="200">
                <template #default="scope">
                    <div v-if="scope.row.drone_image && scope.row.drone_image.length > 0" class="flex flex-wrap gap-2">
                        <el-image
                            v-for="(img, idx) in scope.row.drone_image"
                            :key="idx"
                            style="width: 60px; height: 60px"
                            :src="img"
                            :preview-src-list="scope.row.drone_image"
                            :hide-on-click-modal="true"
                            :preview-teleported="true"
                            :fit="'cover'"
                        />
                    </div>
                    <span v-else>-</span>
                </template>
            </el-table-column>

            <el-table-column label="购买合同" min-width="200">
                <template #default="scope">
                    <div v-if="scope.row.contract_image && scope.row.contract_image.length > 0" class="flex flex-wrap gap-2">
                        <el-image
                            v-for="(img, idx) in scope.row.contract_image"
                            :key="idx"
                            style="width: 60px; height: 60px"
                            :src="img"
                            :preview-src-list="scope.row.contract_image"
                            :hide-on-click-modal="true"
                            :preview-teleported="true"
                            :fit="'cover'"
                        />
                    </div>
                    <span v-else>-</span>
                </template>
            </el-table-column>
            <el-table-column label="维护记录" min-width="120">
                <template #default="scope">
                    <el-button type="primary" link @click="viewMaintainRecords(scope.row.id)">
                        查看记录
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column prop="create_time" label="添加时间" min-width="180" />
        </el-table>
        <div class="flex justify-end mt-4" v-if="dronePager.total > 0">
            <el-pagination
                v-model:current-page="dronePager.page"
                v-model:page-size="dronePager.size"
                :page-sizes="[10, 20, 50, 100]"
                :total="dronePager.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="getDroneList"
                @current-change="getDroneList"
            />
        </div>
    </el-card>

    <!-- 维护记录弹窗 -->
    <el-dialog v-model="maintainDialogVisible" title="维护记录" width="800px">
        <el-table :data="maintainList" v-loading="maintainLoading" style="width: 100%">
            <el-table-column prop="create_time" label="维护时间" min-width="180" />
            <el-table-column prop="content" label="维护内容" min-width="200" />
        </el-table>
        <div class="flex justify-end mt-4" v-if="maintainPager.total > 0">
            <el-pagination
                v-model:current-page="maintainPager.page"
                v-model:page-size="maintainPager.size"
                :page-sizes="[10, 20, 50]"
                :total="maintainPager.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="getMaintainList"
                @current-change="getMaintainList"
            />
        </div>
    </el-dialog>

</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { apiMasterWorkerDetail } from '@/api/master_worker'
import { apiStaffDroneLists, apiStaffDroneMaintain } from '@/api/master_worker/drone'

const route = useRoute()
const detail = ref<any>({})

// 无人机列表相关
const droneList = ref<any[]>([])
const droneLoading = ref(false)
const dronePager = ref({
    page: 1,
    size: 10,
    total: 0
})

// 维护记录相关
const maintainDialogVisible = ref(false)
const maintainList = ref<any[]>([])
const maintainLoading = ref(false)
const maintainPager = ref({
    page: 1,
    size: 10,
    total: 0
})
const currentDroneId = ref<number | string>('')


// 无人机型号转换函数
const getDroneTypeName = (droneTypeId: number) => {
    const droneTypes: { [key: number]: string } = {
        1: '小型',
        2: '中型',
        3: '大型'
    }
    return droneTypes[droneTypeId] || '-'
}

// 获取飞手详情
const getDetail = async () => {
    try {
        const res = await apiMasterWorkerDetail({ id: route.query.id })
        detail.value = res
        // 获取详情后，加载无人机列表
        getDroneList()
    } catch (error) {
        console.error('获取飞手详情失败:', error)
    }
}

// 获取无人机列表
const getDroneList = async () => {
    if (!route.query.id) return

    droneLoading.value = true
    try {
        const res = await apiStaffDroneLists({
            staff_id: route.query.id,
            page_no: dronePager.value.page,
            page_size: dronePager.value.size
        })
        droneList.value = res.lists || []
        dronePager.value.total = res.count || 0
    } catch (error) {
        console.error('获取无人机列表失败:', error)
    } finally {
        droneLoading.value = false
    }
}

// 查看维护记录
const viewMaintainRecords = (droneId: number | string) => {
    currentDroneId.value = droneId
    maintainPager.value.page = 1
    maintainDialogVisible.value = true
    getMaintainList()
}

// 获取维护记录列表
const getMaintainList = async () => {
    if (!currentDroneId.value) return

    maintainLoading.value = true
    try {
        const res = await apiStaffDroneMaintain({
            staff_drone_id: currentDroneId.value,
            page_no: maintainPager.value.page,
            page_size: maintainPager.value.size
        })
        maintainList.value = res.lists || []
        maintainPager.value.total = res.count || 0
    } catch (error) {
        console.error('获取维护记录失败:', error)
    } finally {
        maintainLoading.value = false
    }
}

onMounted(() => {
    getDetail()
})
</script>
