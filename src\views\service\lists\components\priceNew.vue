<template>
    <div>
        <el-form-item label="单位">
            <el-input v-model="formData.unit" placeholder="请输入单位" class="ls-input" v-input-filter="'text'" />
        </el-form-item>
        <el-form-item label="计费方式">
            <el-radio-group v-model="formData.goods_type">
                <el-radio :label="1">按数量计费</el-radio>
                <el-radio :label="2">按单位收费</el-radio>
            </el-radio-group>
        </el-form-item>

        <!-- 按数量计费 -->
        <div v-if="formData.goods_type === 1">
            <el-form-item label="无人机类型">
                <el-select v-model="selectedDroneTypes" multiple placeholder="请选择无人机类型" @change="handleDroneTypeChange" class="ls-input">
                    <el-option
                        v-for="item in droneTypes"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="价格设置">
                <el-table :data="quantityTableData" style="width: 100%">
                    <el-table-column prop="name" label="无人机类型" />
                    <el-table-column label="价格">
                        <template #default="scope">
                            <el-input v-model="scope.row.price" placeholder="请输入价格" />
                        </template>
                    </el-table-column>
                </el-table>
            </el-form-item>
        </div>

        <!-- 按单位收费 -->
        <div v-if="formData.goods_type === 2">
            <el-form-item label="价格设置">
                <el-button type="primary" @click="addUnitRow">添加</el-button>
            </el-form-item>
            <el-form-item>
                <el-table :data="unitTableData" style="width: 100%">
                    <el-table-column label="最小值">
                        <template #default="scope">
                            <el-input v-model="scope.row.min_value" :disabled="true" v-input-filter="'number'"/>
                        </template>
                    </el-table-column>
                    <el-table-column label="最大值">
                        <template #default="scope">
                            <el-input v-model="scope.row.max_value" v-input-filter="'number'" />
                        </template>
                    </el-table-column>
                    <el-table-column label="无人机类型">
                        <template #default="scope">
                            <el-select v-model="scope.row.drone_type_id" placeholder="请选择">
                                <el-option
                                    v-for="item in droneTypes"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="单价">
                        <template #default="scope">
                            <el-input v-model="scope.row.price" placeholder="请输入单价" v-input-filter="'money'" />
                        </template>
                    </el-table-column>
                    <el-table-column label="操作">
                        <template #default="scope">
                            <el-button
                                v-if="scope.$index === unitTableData.length - 1 && unitTableData.length > 1"
                                type="danger"
                                size="small"
                                @click="removeUnitRow"
                                >删除</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </el-form-item>
        </div>
    </div>
</template>

<script lang="ts" setup>
import feedback from '@/utils/feedback'
import { ref, watch, computed, defineExpose } from 'vue'

const props = defineProps<{ modelValue: any }>()
const emit = defineEmits(['update:modelValue'])

const formData = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
})

// --- Internal State ---
const quantityTableData = ref<{ id: number; name: string; price: string }[]>([])
const unitTableData = ref<any[]>([])
const selectedDroneTypes = ref<number[]>([])

// --- State for Edit-Mode-Restore ---
const isEditMode = ref(false)
const originalGoodsType = ref<number | null>(null)
const originalPriceRules = ref<any[]>([])

// --- Constants ---
const droneTypes = ref([
    { id: 1, name: '小型' },
    { id: 2, name: '中型' },
    { id: 3, name: '大型' }
])

// --- Methods ---
const addUnitRow = () => {
    const lastRow =
        unitTableData.value.length > 0 ? unitTableData.value[unitTableData.value.length - 1] : null

    if (lastRow) {
        if (!lastRow.max_value) {
            return feedback.msgError('请输入最大值')
        }
        if (Number(lastRow.max_value) <= Number(lastRow.min_value)) {
            return feedback.msgError('最大值必须大于最小值')
        }
    }
    unitTableData.value.push({
        min_value: lastRow ? lastRow.max_value : 1,
        max_value: '',
        drone_type_id: '',
        price: ''
    })
}

const removeUnitRow = () => {
    if (unitTableData.value.length > 1) {
        unitTableData.value.pop()
    }
}

const handleDroneTypeChange = (selectedIds: number[]) => {
    quantityTableData.value = selectedIds.map((id) => {
        const existing = quantityTableData.value.find((item) => item.id === id)
        const drone = droneTypes.value.find((item) => item.id === id)
        return {
            id: id,
            name: drone ? drone.name : '',
            price: existing ? existing.price : ''
        }
    })
}

// This is the ONLY function that provides data to the parent.
const getServicePriceRules = () => {
    if (props.modelValue.goods_type === 1) {
        return quantityTableData.value.map((item) => ({
            min_value: 0,
            max_value: 0,
            drone_type_id: item.id,
            price: item.price
        }))
    } else {
        return unitTableData.value
    }
}
defineExpose({ getServicePriceRules })

// --- Watchers ---

// Watcher 1: One-time initialization.
watch(
    () => props.modelValue,
    (newVal) => {
        const price_rules = newVal.service_price_rules || []
        if (newVal.goods_type === 1) {
            quantityTableData.value = price_rules.map((r: any) => ({
                id: r.drone_type_id,
                name: droneTypes.value.find((d) => d.id === r.drone_type_id)?.name || '',
                price: r.price
            }))
            selectedDroneTypes.value = price_rules.map((r: any) => r.drone_type_id)
        } else {
            unitTableData.value = JSON.parse(JSON.stringify(price_rules))
            if (unitTableData.value.length === 0) {
                addUnitRow()
            }
        }

        if (newVal.id) {
            isEditMode.value = true
            originalGoodsType.value = newVal.goods_type
            originalPriceRules.value = JSON.parse(JSON.stringify(price_rules))
        }
    },
    { once: true } // This watcher only runs once.
)

// Watcher 2: Handles switching goods_type.
watch(
    () => props.modelValue.goods_type,
    (newVal, oldVal) => {
        if (newVal === oldVal) return

        if (isEditMode.value && newVal === originalGoodsType.value) {
            const rules = originalPriceRules.value
            if (newVal === 1) {
                quantityTableData.value = rules.map((r: any) => ({
                    id: r.drone_type_id,
                    name: droneTypes.value.find((d) => d.id === r.drone_type_id)?.name || '',
                    price: r.price
                }))
                selectedDroneTypes.value = rules.map((r: any) => r.drone_type_id)
                unitTableData.value = []
            } else {
                unitTableData.value = JSON.parse(JSON.stringify(rules))
                quantityTableData.value = []
                selectedDroneTypes.value = []
            }
        } else {
            quantityTableData.value = []
            selectedDroneTypes.value = []
            unitTableData.value = []
            if (newVal === 2) {
                addUnitRow()
            }
        }
    }
)
</script>

<style lang="scss" scoped>
.ls-input {
    width: 460px;
}
</style>