/**
 * 富文本内容过滤器
 * 用于过滤富文本编辑器中的JS和超链接
 */

import type { Editor } from '@wangeditor/editor'

/**
 * 富文本内容过滤器类
 */
export class RichTextFilter {
    private editor: Editor
    private filterTimeout: any = null
    private isDestroyed = false
    private lastSafeContent = ''
    
    constructor(editor: Editor) {
        this.editor = editor
        // 初始化时保存安全内容
        this.lastSafeContent = editor.getHtml()
    }
    
    /**
     * 实时过滤（仅过滤危险内容，不干扰用户体验）
     */
    immediateFilter(): void {
        if (this.isDestroyed || !this.editor) return
        
        try {
            const content = this.editor.getHtml()
            const filteredContent = this.filterDangerousOnly(content)
            
            if (content !== filteredContent) {
                this.editor.setHtml(filteredContent)
                this.lastSafeContent = filteredContent
            }
        } catch (error) {
            console.debug('实时过滤错误:', error)
        }
    }
    
    /**
     * 完整过滤（在失去焦点时执行）
     */
    fullFilter(): void {
        if (this.isDestroyed || !this.editor) return
        
        try {
            const content = this.editor.getHtml()
            const filteredContent = this.filterHtml(content)
            
            if (content !== filteredContent) {
                this.editor.setHtml(filteredContent)
                this.lastSafeContent = filteredContent
            }
        } catch (error) {
            console.debug('完整过滤错误:', error)
        }
    }
    
    /**
     * 仅过滤危险内容（实时使用）
     */
    private filterDangerousOnly(html: string): string {
        if (!html) return html
        
        let filtered = html
        
        // 仅过滤最危险的内容
        filtered = filtered.replace(/<script\b[^>]*>[\s\S]*?<\/script>/gi, '')
        filtered = filtered.replace(/javascript\s*:/gi, '')
        filtered = filtered.replace(/data\s*:\s*text\/html/gi, '')
        filtered = filtered.replace(/vbscript\s*:/gi, '')
        
        return filtered
    }
    
    /**
     * 过滤HTML内容
     */
    private filterHtml(html: string): string {
        return RichTextFilter.filterHtmlStatic(html)
    }
    
    /**
     * 静态方法：过滤HTML内容
     */
    static filterHtmlStatic(html: string): string {
        if (!html) return html
        
        let filtered = html
        
        // 过滤<script>标签及其内容
        filtered = filtered.replace(/<script\b[^>]*>[\s\S]*?<\/script>/gi, '')
        
        // 过滤javascript:协议（在任何地方）
        filtered = filtered.replace(/javascript\s*:/gi, '')
        
        // 过滤所有事件属性（onclick、onload等）
        filtered = filtered.replace(/<([^>]+)\s+on\w+\s*=\s*["'][^"']*["']/gi, '<$1')
        
        // 过滤data:协议（可能用于执行JS）
        filtered = filtered.replace(/data\s*:\s*text\/html/gi, '')
        
        // 过滤vbscript:协议
        filtered = filtered.replace(/vbscript\s*:/gi, '')
        
        // 过滤<a>标签的href属性（移除所有href，保留链接文本）
        filtered = filtered.replace(/<a\b[^>]*href=["'][^"']*["'][^>]*>/gi, (match) => {
            // 移除href属性，但保留其他属性和标签结构
            return match.replace(/href=["'][^"']*["']/gi, '')
        })
        
        // 过滤iframe标签及其内容
        filtered = filtered.replace(/<iframe\b[^>]*>[\s\S]*?<\/iframe>/gi, '')
        
        // 过滤object、embed、applet等标签
        filtered = filtered.replace(/<(object|embed|applet)\b[^>]*>[\s\S]*?<\/\1>/gi, '')
        
        // 过滤form标签及其内容
        filtered = filtered.replace(/<form\b[^>]*>[\s\S]*?<\/form>/gi, '')
        
        // 过滤input、button、select、textarea等表单元素
        filtered = filtered.replace(/<(input|button|select|textarea)\b[^>]*>/gi, '')
        
        return filtered
    }
    
    /**
     * 设置内容变化监听器
     */
    setupChangeListener(): void {
        // 粘贴事件 - 立即过滤危险内容
        this.editor.on('paste', () => {
            setTimeout(() => {
                this.immediateFilter()
            }, 50)
        })
        
        // 失去焦点时 - 执行完整过滤
        this.editor.on('blur', () => {
            setTimeout(() => {
                this.fullFilter()
            }, 100)
        })
        
        // 内容变化时 - 轻度过滤，延迟2秒执行
        this.editor.on('change', () => {
            // 清除之前的定时器
            if (this.filterTimeout) {
                clearTimeout(this.filterTimeout)
            }
            
            this.filterTimeout = setTimeout(() => {
                this.immediateFilter()
            }, 2000)
        })
    }
    
    /**
     * 销毁过滤器，清理定时器和事件监听器
     */
    destroy(): void {
        this.isDestroyed = true
        
        if (this.filterTimeout) {
            clearTimeout(this.filterTimeout)
            this.filterTimeout = null
        }
        
        // WangEditor 的事件监听器会随着编辑器销毁自动清理，不需要手动移除
        // 只需要标记过滤器为已销毁状态
        this.editor = null as any
    }
}

/**
 * 为富文本编辑器添加内容过滤
 * @param editor 富文本编辑器实例
 */
export function setupRichTextFilter(editor: Editor): void {
    const filter = new RichTextFilter(editor)
    filter.setupChangeListener()
}

/**
 * 导出过滤HTML函数，供外部使用
 * @param html HTML内容
 */
export function filterHtml(html: string): string {
    return RichTextFilter.filterHtmlStatic(html)
}