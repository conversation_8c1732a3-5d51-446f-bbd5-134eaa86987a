<template>
    <div class="edit-popup">
        <popup
            ref="popupRef"
            :title="props.staff_id ? '更换飞手' : '分配飞手'"
            :async="true"
            width="900px"
            @confirm="handleSubmit"
            @close="handleClose"
        >
            <div class="staff-selection">
                <!-- 搜索区域 -->
                <div class="search-area mb-4">
                    <el-row :gutter="16">
                        <el-col :span="8">
                            <el-input
                                v-model="searchForm.mobile"
                                placeholder="请输入手机号码"
                                clearable
                                @input="handleSearch"
                            />
                        </el-col>
                        <el-col :span="16">
                            <area-select
                                v-model:province-id="searchForm.province_id"
                                v-model:city-id="searchForm.city_id"
                                v-model:district-id="searchForm.district_id"
                                @change="handleSearch"
                                placeholder="请选择所属地区"
                            />
                        </el-col>
                    </el-row>
                </div>

                <!-- 飞手列表 -->
                <div class="staff-list">
                    <el-scrollbar height="400px">
                        <el-table
                            :data="pager.lists"
                            style="width: 100%"
                            @row-click="handleRowClick"
                            v-loading="pager.loading"
                        >
                            <el-table-column width="50">
                                <template #default="{ row }">
                                    <el-radio
                                        :label="row.id"
                                        :model-value="formData.staff_id"
                                        @change="handleSelectStaff(row)"
                                    >&nbsp;</el-radio>
                                </template>
                            </el-table-column>
                            <el-table-column label="头像" width="80">
                                <template #default="{ row }">
                                    <el-avatar
                                        :src="row.work_image"
                                        :size="40"
                                        fit="cover"
                                    >
                                        {{ row.name?.charAt(0) }}
                                    </el-avatar>
                                </template>
                            </el-table-column>
                            <el-table-column label="昵称" prop="name" min-width="100" />
                            <el-table-column label="手机号码" prop="mobile" min-width="120" />
                            <el-table-column label="所属地区" min-width="200">
                                <template #default="{ row }">
                                    {{ row.province }}{{ row.city }}{{ row.district }}
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-scrollbar>
                </div>

                <!-- 分页 -->
                <div class="pagination-area mt-4 flex justify-end">
                    <pagination
                        v-model="pager"
                        @change="getStaffLists"
                        layout="total, prev, pager, next, jumper"
                    />
                </div>

                <!-- 温馨提示 -->
                <div class="tips-area mt-4">
                    <el-alert
                        title="温馨提示"
                        type="info"
                        :closable="false"
                        show-icon
                    >
                        <div>需满足下列条件，否则无法选择飞手：</div>
                        <div>1、当前服务位置是否在飞手可接单区域内</div>
                        <div>2、只有飞手选择了该服务项目才能接单</div>
                        <div>3、飞手的可服务时间与项目要求相符</div>
                    </el-alert>
                </div>
            </div>
        </popup>
    </div>
</template>
<script lang="ts" setup>
import type { FormInstance } from 'element-plus'
import Popup from '@/components/popup/index.vue'
import Pagination from '@/components/pagination/index.vue'
import AreaSelect from '@/components/area-select/index.vue'
import feedback from '@/utils/feedback'
import { apiDispatchStaff } from '@/api/order/lists'
import { apiMasterWorkerLists } from '@/api/master_worker/index'
import { usePaging } from '@/hooks/usePaging'

const props = defineProps({
    id: { type: Number },
    staff_id: { type: [String, Number] }
})
const emit = defineEmits(['success', 'close'])
const formRef = shallowRef<FormInstance>()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const formData = reactive({
    id: props.id,
    staff_id: props.staff_id
})

// 搜索表单
const searchForm = reactive({
    mobile: '',
    province_id: '',
    city_id: '',
    district_id: ''
})

// 分页组件
const { pager, getLists: getStaffLists, resetPage } = usePaging({
    fetchFun: apiMasterWorkerLists,
    params: searchForm
})

// 搜索处理
const handleSearch = () => {
    resetPage()
}

// 选择飞手
const handleSelectStaff = (row: any) => {
    formData.staff_id = row.id
}

// 点击行选择
const handleRowClick = (row: any) => {
    formData.staff_id = row.id
}

const open = () => {
    console.log(formData.staff_id)
    // 重置搜索条件
    Object.assign(searchForm, {
        mobile: '',
        province_id: '',
        city_id: '',
        district_id: ''
    })
    // 获取飞手列表
    getStaffLists()
    popupRef.value?.open()
}

const handleClose = () => {
    emit('close')
}

defineExpose({
    open
})

//提交
const handleSubmit = async () => {
    if (!formData.staff_id) {
        feedback.msgError('请选择飞手')
        return
    }
    await apiDispatchStaff(formData)
    popupRef.value?.close()
    emit('success')
}
</script>

<style lang="scss" scoped>
.staff-selection {
    .search-area {
        padding: 16px;
        background-color: #f5f7fa;
        border-radius: 4px;
    }

    .staff-list {
        :deep(.el-table__row) {
            cursor: pointer;

            &:hover {
                background-color: #f5f7fa;
            }
        }
    }

    .pagination-area {
        border-top: 1px solid #ebeef5;
        padding-top: 16px;
    }

    .tips-area {
        :deep(.el-alert__content) {
            line-height: 1.6;
        }
    }
}
</style>
